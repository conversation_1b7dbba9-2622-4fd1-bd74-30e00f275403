{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "devc": "rm -rf .next && next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-slot": "^1.2.1", "@types/negotiator": "^0.6.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.9.7", "install": "^0.13.0", "jotai": "^2.12.3", "lucide-react": "^0.507.0", "negotiator": "^1.0.0", "next": "15.3.1", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify-json/devicon": "^1.2.24", "@iconify-json/simple-icons": "^1.2.36", "@iconify/tailwind4": "^1.0.6", "@next/eslint-plugin-next": "^15.3.0", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "^5"}}